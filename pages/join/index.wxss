/**join.wxss**/
.nav-bar {
  justify-content: space-between;
}

.nav-back {
  width: 40rpx;
  display: flex;
  align-items: center;
}

.back-icon {
  font-size: 36rpx;
  font-weight: 300;
}

.nav-placeholder {
  width: 40rpx;
}

/* 游戏信息卡片 */
.game-info-card {
  background-color: #ffffff;
  border-radius: 12rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
}

.game-header {
  text-align: center;
}

.game-name {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 12rpx;
}

.game-details {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.initial-score {
  font-size: 24rpx;
  color: #999;
}

/* 用户表单卡片 */
.user-form-card {
  background-color: #ffffff;
  border-radius: 12rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
}

.form-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 32rpx;
  text-align: center;
}

.form-group {
  margin-bottom: 32rpx;
}

.form-group.horizontal {
  display: flex;
  align-items: center;
  gap: 24rpx;
}

.form-label {
  font-size: 28rpx;
  color: #666;
  font-weight: 500;
  flex-shrink: 0;
  width: 80rpx;
}

.form-group.horizontal .form-label {
  margin-bottom: 0;
}

.form-group:not(.horizontal) .form-label {
  margin-bottom: 16rpx;
}

.form-input {
  height: 80rpx;
  border-radius: 8rpx;
  background-color: #f5f5f5;
  padding: 0 24rpx;
  font-size: 28rpx;
  width: 100%;
  box-sizing: border-box;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
}

.form-group.horizontal .form-input {
  flex: 1;
}

.form-input:focus {
  background-color: #ffffff;
  border-color: #1296db;
  box-shadow: 0 0 0 2rpx rgba(18, 150, 219, 0.2);
}

.form-input[type="nickname"] {
  background-color: #e6f7ff;
  border: 1rpx solid #91d5ff;
}

.form-input[type="nickname"]:focus {
  background-color: #ffffff;
  border-color: #1296db;
}

.avatar-selector {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24rpx;
  flex: 1;
}

.avatar-wrapper {
  background: none;
  border: none;
  padding: 0;
  margin: 0;
  border-radius: 60rpx;
  overflow: hidden;
  position: relative;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.avatar-wrapper::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 60rpx;
  border: 2rpx solid #e0e0e0;
  pointer-events: none;
  transition: border-color 0.3s ease;
}

.avatar-wrapper:hover::after {
  border-color: #1296db;
}

.avatar-wrapper:active {
  transform: scale(0.96);
}

.current-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  display: block;
}

.avatar-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.2);
  opacity: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 60rpx;
  transition: opacity 0.3s ease;
}

.avatar-wrapper:hover .avatar-overlay {
  opacity: 1;
}

.avatar-edit-icon {
  color: #fff;
  font-size: 40rpx;
  font-weight: 600;
}

.avatar-btn {
  padding: 12rpx 24rpx;
  border-radius: 30rpx;
  font-size: 28rpx;
  border: none;
  transition: all 0.3s ease;
  min-height: 70rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #1296db, #0d7bc7);
  color: #ffffff;
  box-shadow: 0 4rpx 12rpx rgba(18, 150, 219, 0.3);
  width: 80%;
  margin: 0 auto;
}

.avatar-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 6rpx rgba(18, 150, 219, 0.3);
  background: linear-gradient(135deg, #0d7bc7, #0d6db0);
}

.avatar-btn-icon {
  margin-right: 8rpx;
  font-size: 30rpx;
}

.form-actions {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
  margin-top: 24rpx;
}

.join-btn {
  width: 100%;
  height: 88rpx;
  background-color: #1296db;
  color: #ffffff;
  border-radius: 8rpx;
  font-size: 32rpx;
  font-weight: 500;
  border: none;
}

.join-btn[disabled] {
  background-color: #cccccc !important;
  color: #ffffff !important;
  opacity: 0.6;
}

.guide-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  width: 100%;
  height: 64rpx;
  background-color: #f0f0f0;
  color: #666;
  border-radius: 8rpx;
  font-size: 26rpx;
  border: none;
  transition: all 0.3s ease;
}

.guide-btn:hover {
  background-color: #e0e0e0;
}

.guide-icon {
  font-size: 28rpx;
}

.guide-text {
  font-size: 26rpx;
}

/* 已加入状态 */
.joined-status {
  background-color: #ffffff;
  border-radius: 12rpx;
  padding: 48rpx 32rpx;
  margin-bottom: 32rpx;
  text-align: center;
}

.status-icon {
  font-size: 80rpx;
  color: #4CAF50;
  margin-bottom: 24rpx;
}

.status-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 12rpx;
}

.status-desc {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 32rpx;
}

.player-info-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
  padding: 24rpx;
  background-color: #f8f8f8;
  border-radius: 12rpx;
}

.joined-avatar {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50rpx;
}

.joined-name {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
}

/* 游戏状态卡片 */
.game-status-card {
  background-color: #ffffff;
  border-radius: 12rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
}

.status-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 24rpx;
}

.status-info {
  margin-bottom: 24rpx;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12rpx 0;
}

.status-label {
  font-size: 28rpx;
  color: #666;
}

.status-value {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
}

.refresh-btn {
  width: 100%;
  height: 72rpx;
  background-color: #f0f0f0;
  color: #333;
  border-radius: 8rpx;
  font-size: 28rpx;
  border: none;
}

/* 错误提示卡片 */
.error-card {
  background-color: #fff2f0;
  border: 1rpx solid #ffccc7;
  border-radius: 12rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  text-align: center;
}

.error-icon {
  font-size: 48rpx;
  margin-bottom: 16rpx;
}

.error-text {
  font-size: 28rpx;
  color: #d4380d;
  margin-bottom: 24rpx;
  line-height: 1.5;
}

.retry-btn {
  padding: 16rpx 32rpx;
  background-color: #1296db;
  color: #fff;
  border-radius: 8rpx;
  font-size: 28rpx;
  border: none;
}

/* 响应式布局优化 */
@media screen and (max-width: 600rpx) {
  .form-group.horizontal {
    flex-direction: column;
    align-items: flex-start;
    gap: 16rpx;
  }

  .form-group.horizontal .form-label {
    width: auto;
    margin-bottom: 8rpx;
  }

  .avatar-selector {
    flex-direction: column;
    align-items: center;
    gap: 24rpx;
  }

  .current-avatar {
    width: 120rpx;
    height: 120rpx;
  }

  .avatar-btn {
    width: 100%;
    min-height: 70rpx;
    font-size: 28rpx;
  }
}