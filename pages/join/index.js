// join.js
const router = require('../../utils/router');

Page({
    data: {
        statusBarHeight: 20,
        inviteId: '',
        gameInfo: null,
        gameTypeName: '',
        playerName: '',
        playerAvatar: '',
        defaultAvatarUrl: 'https://mmbiz.qpic.cn/mmbiz/icTdbqWNOwNRna42FI242Lcia07jQodd2FJGIYQfG0LAJGFxM4FbnQP6yfMxBgJ0F3YRqJCJ1aPAK2dQagdusBZg/0',
        hasJoined: false,
        canJoin: false,
        joinedCount: 0,
        statusText: '等待中',
        errorMessage: '',

    },

    onLoad(options) {
        // 获取系统信息设置状态栏高度
        const systemInfo = wx.getSystemInfoSync();
        this.setData({
            statusBarHeight: systemInfo.statusBarHeight
        });

        // 获取邀请ID
        const inviteId = options.inviteId;
        if (!inviteId) {
            this.setData({
                errorMessage: '邀请链接无效'
            });
            return;
        }

        this.setData({
            inviteId
        });

        // 加载邀请信息
        this.loadInviteInfo();

        // 尝试获取用户信息
        this.getUserInfo();
    },

    onShow() {
        // 每次显示页面时刷新状态
        if (this.data.inviteId) {
            this.refreshStatus();
        }
    },

    navigateBack() {
        router.navigateBack();
    },

    loadInviteInfo() {
        // 从本地存储获取邀请信息（实际项目中应该从服务器获取）
        const inviteInfo = wx.getStorageSync(`invite_${this.data.inviteId}`);

        if (!inviteInfo) {
            this.setData({
                errorMessage: '邀请信息不存在或已过期'
            });
            return;
        }

        const gameTypeName = this.getGameTypeName(inviteInfo.gameInfo.type);

        this.setData({
            gameInfo: inviteInfo.gameInfo,
            gameTypeName
        });

        this.refreshStatus();
    },

    getUserInfo() {
        // 尝试从本地获取用户信息
        const userInfo = wx.getStorageSync('userInfo');
        if (userInfo) {
            this.setData({
                playerName: userInfo.nickName || '',
                playerAvatar: userInfo.avatarUrl || this.data.defaultAvatarUrl
            });
        }

        this.checkCanJoin();
    },

    // 新的微信头像选择方法
    onChooseAvatar(e) {
        const {
            avatarUrl
        } = e.detail;
        console.log('选择头像:', avatarUrl);

        this.setData({
            playerAvatar: avatarUrl
        });

        // 保存到本地存储
        const userInfo = wx.getStorageSync('userInfo') || {};
        userInfo.avatarUrl = avatarUrl;
        wx.setStorageSync('userInfo', userInfo);

        this.checkCanJoin();

        wx.showToast({
            title: '头像已更新',
            icon: 'success',
            duration: 1500
        });
    },

    // 昵称输入失焦处理
    onNicknameBlur(e) {
        const nickName = e.detail.value.trim();
        if (nickName) {
            // 保存到本地存储
            const userInfo = wx.getStorageSync('userInfo') || {};
            userInfo.nickName = nickName;
            wx.setStorageSync('userInfo', userInfo);
        }
    },



    getGameTypeName(type) {
        const typeMap = {
            'mahjong': '麻将',
            'poker': '扑克',
            'board': '棋类',
            'other': '其他'
        };
        return typeMap[type] || '未知';
    },

    onPlayerNameInput(e) {
        this.setData({
            playerName: e.detail.value
        });
        this.checkCanJoin();
    },



    checkCanJoin() {
        const {
            playerName,
            hasJoined
        } = this.data;
        const canJoin = playerName.trim() !== '' && !hasJoined;

        this.setData({
            canJoin
        });
    },

    // 显示用户信息获取指引
    showUserInfoGuide() {
        wx.showModal({
            title: '用户信息获取说明',
            content: '请填写您的游戏昵称，并点击"选择微信头像"来设置您的头像。',
            showCancel: false,
            confirmText: '我知道了'
        });
    },

    joinGame() {
        if (!this.data.canJoin) {
            return;
        }

        wx.showLoading({
            title: '加入中...'
        });

        // 模拟加入游戏
        setTimeout(() => {
            const inviteInfo = wx.getStorageSync(`invite_${this.data.inviteId}`);

            if (!inviteInfo) {
                wx.hideLoading();
                this.setData({
                    errorMessage: '邀请信息不存在'
                });
                return;
            }

            // 检查是否已经加入
            const existingPlayer = inviteInfo.players.find(p =>
                p.name === this.data.playerName
            );

            if (existingPlayer) {
                wx.hideLoading();
                this.setData({
                    hasJoined: true,
                    errorMessage: ''
                });
                this.refreshStatus();
                return;
            }

            // 检查人数是否已满
            const joinedPlayers = inviteInfo.players.filter(p => p.status === 'joined');
            if (joinedPlayers.length >= inviteInfo.gameInfo.playerCount) {
                wx.hideLoading();
                this.setData({
                    errorMessage: '游戏人数已满'
                });
                return;
            }

            // 添加新玩家
            const newPlayer = {
                id: Date.now().toString(),
                name: this.data.playerName,
                avatar: this.data.playerAvatar || '/static/images/default-avatar.png',
                status: 'joined',
                joinTime: Date.now()
            };

            inviteInfo.players.push(newPlayer);

            // 保存更新后的邀请信息
            wx.setStorageSync(`invite_${this.data.inviteId}`, inviteInfo);

            wx.hideLoading();

            this.setData({
                hasJoined: true,
                errorMessage: ''
            });

            this.refreshStatus();

            wx.showToast({
                title: '加入成功',
                icon: 'success'
            });
        }, 1000);
    },

    refreshStatus() {
        const inviteInfo = wx.getStorageSync(`invite_${this.data.inviteId}`);

        if (!inviteInfo) {
            this.setData({
                errorMessage: '邀请信息不存在'
            });
            return;
        }

        const joinedPlayers = inviteInfo.players.filter(p => p.status === 'joined');
        const joinedCount = joinedPlayers.length;
        const totalCount = inviteInfo.gameInfo.playerCount;

        let statusText = '等待中';
        if (joinedCount >= totalCount) {
            statusText = '人数已满，等待开始';
        } else {
            statusText = `还需${totalCount - joinedCount}人`;
        }

        // 检查当前用户是否已加入
        const currentUserJoined = inviteInfo.players.some(p =>
            p.name === this.data.playerName && p.status === 'joined'
        );

        this.setData({
            joinedCount,
            statusText,
            hasJoined: currentUserJoined
        });

        this.checkCanJoin();
    },

    retryJoin() {
        this.setData({
            errorMessage: ''
        });
        this.loadInviteInfo();
    }
});