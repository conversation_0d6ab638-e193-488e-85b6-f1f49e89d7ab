<!--join.wxml-->
<view class="container">
  <!-- 状态栏 -->
  <view class="status-bar" style="height: {{statusBarHeight}}px;"></view>
  
  <!-- 导航栏 -->
  <view class="nav-bar">
    <view class="nav-back" bindtap="navigateBack">
      <text class="back-icon">‹</text>
    </view>
    <view class="nav-title">加入游戏</view>
    <view class="nav-placeholder"></view>
  </view>
  
  <!-- 内容区域 -->
  <view class="content">
    <!-- 游戏信息 -->
    <view class="game-info-card" wx:if="{{gameInfo}}">
      <view class="game-header">
        <view class="game-name">{{gameInfo.name}}</view>
        <view class="game-details">{{gameInfo.playerCount}}人 · {{gameTypeName}}</view>
        <view class="initial-score">初始分数：{{gameInfo.initialScore}}</view>
      </view>
    </view>

    <!-- 用户信息输入 -->
    <view class="user-form-card" wx:if="{{!hasJoined}}">
      <view class="form-title">请输入你的游戏信息</view>


      
      <view class="form-group horizontal">
        <view class="form-label">昵称</view>
        <input
          class="form-input"
          type="nickname"
          placeholder="请输入你的昵称"
          value="{{playerName}}"
          bindinput="onPlayerNameInput"
          bindblur="onNicknameBlur"
        />
      </view>

      <view class="form-group horizontal">
        <view class="form-label">头像</view>
        <view class="avatar-selector">
          <button
            class="avatar-wrapper"
            open-type="chooseAvatar"
            bindchooseavatar="onChooseAvatar"
          >
            <image
              class="current-avatar"
              src="{{playerAvatar || defaultAvatarUrl}}"
              mode="aspectFill"
            ></image>
            <view class="avatar-overlay">
              <view class="avatar-edit-icon">+</view>
            </view>
          </button>
          <button class="avatar-btn primary" open-type="chooseAvatar" bindchooseavatar="onChooseAvatar">
            <text class="avatar-btn-icon">👤</text>
            <text>选择微信头像</text>
          </button>
        </view>
      </view>

      <view class="form-actions">
        <button
          class="join-btn"
          bindtap="joinGame"
          disabled="{{!canJoin}}"
        >
          加入游戏
        </button>

        <button class="guide-btn" bindtap="showUserInfoGuide">
          <text class="guide-icon">❓</text>
          <text class="guide-text">获取信息说明</text>
        </button>
      </view>
    </view>

    <!-- 已加入状态 -->
    <view class="joined-status" wx:if="{{hasJoined}}">
      <view class="status-icon">✓</view>
      <view class="status-title">已成功加入游戏</view>
      <view class="status-desc">等待游戏开始...</view>
      
      <view class="player-info-card">
        <image class="joined-avatar" src="{{playerAvatar}}" mode="aspectFill"></image>
        <view class="joined-name">{{playerName}}</view>
      </view>
    </view>

    <!-- 游戏状态 -->
    <view class="game-status-card">
      <view class="status-title">游戏状态</view>
      <view class="status-info">
        <view class="status-item">
          <text class="status-label">已加入玩家：</text>
          <text class="status-value">{{joinedCount}}/{{gameInfo.playerCount}}</text>
        </view>
        <view class="status-item">
          <text class="status-label">游戏状态：</text>
          <text class="status-value">{{statusText}}</text>
        </view>
      </view>
      
      <button class="refresh-btn" bindtap="refreshStatus">刷新状态</button>
    </view>

    <!-- 错误提示 -->
    <view class="error-card" wx:if="{{errorMessage}}">
      <view class="error-icon">⚠️</view>
      <view class="error-text">{{errorMessage}}</view>
      <button class="retry-btn" bindtap="retryJoin">重试</button>
    </view>
  </view>
</view> 