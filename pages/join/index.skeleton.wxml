<!--
此文件为开发者工具生成，生成时间: 2025/6/20下午11:13:53
使用方法：
在 /Users/<USER>/WeChatProjects/ScoreSubtotal/pages/join/index.wxml 引入模板

```
<import src="index.skeleton.wxml"/>
<template is="skeleton" wx:if="{{loading}}" />
```

在 /Users/<USER>/WeChatProjects/ScoreSubtotal/pages/join/index.wxss 中引入样式
```
@import "./index.skeleton.wxss";
```

更多详细信息可以参考文档：https://developers.weixin.qq.com/miniprogram/dev/devtools/skeleton.html
-->
<template name="skeleton">
  <view class="sk-container">
    <view class="container">
      <view class="status-bar" style="height: 47px;"></view>
      <view class="nav-bar">
        <view class="nav-back">
          <text class="back-icon sk-transparent sk-opacity">‹</text>
        </view>
        <view class="nav-title sk-transparent sk-text-16-6667-417 sk-text" style="background-position-x: 50%;">加入游戏</view>
        <view class="nav-placeholder"></view>
      </view>
      <view class="content">
        <view class="game-info-card">
          <view class="game-header">
            <view class="game-name sk-transparent sk-text-16-6667-578 sk-text" style="background-position-x: 50%;">6月20日游戏</view>
            <view class="game-details sk-transparent sk-text-16-6667-985 sk-text" style="background-position-x: 50%;">2人 · 麻将</view>
            <view class="initial-score sk-transparent sk-text-16-6667-635 sk-text" style="background-position-x: 50%;">初始分数：1000</view>
          </view>
        </view>
        <view class="user-form-card">
          <view class="form-title sk-transparent sk-text-16-6667-136 sk-text" style="background-position-x: 50%;">请输入你的游戏信息</view>
          <view class="form-group">
            <view class="form-label sk-transparent sk-text-16-6667-580 sk-text">昵称</view>
            <view class="form-input sk-image" placeholder="请输入你的昵称" value="true" selection-start="-1" selection-end="-1" cursor="-1"></view>
          </view>
          <view class="form-group">
            <view class="form-label sk-transparent sk-text-16-6667-359 sk-text">头像</view>
            <view class="avatar-selector">
              <image class="current-avatar sk-image" mode="aspectFill"></image>
              <button class="avatar-btn sk-transparent sk-button sk-pseudo sk-pseudo-circle">选择头像</button>
            </view>
          </view>
          <button class="join-btn sk-transparent sk-button sk-pseudo sk-pseudo-circle" disabled="true">
            加入游戏
          </button>
        </view>
        <view class="game-status-card">
          <view class="status-title sk-transparent sk-text-16-6667-365 sk-text">游戏状态</view>
          <view class="status-info">
            <view class="status-item">
              <text class="status-label sk-transparent sk-text-16-6667-385 sk-text">已加入玩家：</text>
              <text class="status-value sk-transparent sk-text-16-6667-286 sk-text">0/2</text>
            </view>
            <view class="status-item">
              <text class="status-label sk-transparent sk-text-16-6667-161 sk-text">游戏状态：</text>
              <text class="status-value sk-transparent sk-text-16-6667-624 sk-text">还需2人</text>
            </view>
          </view>
          <button class="refresh-btn sk-transparent sk-button sk-pseudo sk-pseudo-circle">刷新状态</button>
        </view>
      </view>
    </view>
  </view>
</template>