/*
此文件为开发者工具生成，生成时间: 2025/6/20下午11:13:53

在 /Users/<USER>/WeChatProjects/ScoreSubtotal/pages/join/index.wxss 中引入样式
```
@import "./index.skeleton.wxss";
```

更多详细信息可以参考文档：https://developers.weixin.qq.com/miniprogram/dev/devtools/skeleton.html
*/
.sk-transparent {
    color: transparent !important;
  }
.sk-opacity {
    opacity: 0 !important;
  }
.sk-text-16-6667-417 {
    background-image: linear-gradient(transparent 16.6667%, #EEEEEE 0%, #EEEEEE 83.3333%, transparent 0%) !important;
    background-size: 100% 51.9231rpx;
    position: relative !important;
  }
.sk-text {
    background-origin: content-box !important;
    background-clip: content-box !important;
    background-color: transparent !important;
    color: transparent !important;
    background-repeat: repeat-y !important;
  }
.sk-text-16-6667-578 {
    background-image: linear-gradient(transparent 16.6667%, #EEEEEE 0%, #EEEEEE 83.3333%, transparent 0%) !important;
    background-size: 100% 51.9231rpx;
    position: relative !important;
  }
.sk-text-16-6667-985 {
    background-image: linear-gradient(transparent 16.6667%, #EEEEEE 0%, #EEEEEE 83.3333%, transparent 0%) !important;
    background-size: 100% 40.3846rpx;
    position: relative !important;
  }
.sk-text-16-6667-635 {
    background-image: linear-gradient(transparent 16.6667%, #EEEEEE 0%, #EEEEEE 83.3333%, transparent 0%) !important;
    background-size: 100% 34.6154rpx;
    position: relative !important;
  }
.sk-text-16-6667-136 {
    background-image: linear-gradient(transparent 16.6667%, #EEEEEE 0%, #EEEEEE 83.3333%, transparent 0%) !important;
    background-size: 100% 46.1538rpx;
    position: relative !important;
  }
.sk-text-16-6667-580 {
    background-image: linear-gradient(transparent 16.6667%, #EEEEEE 0%, #EEEEEE 83.3333%, transparent 0%) !important;
    background-size: 100% 40.3846rpx;
    position: relative !important;
  }
.sk-text-16-6667-359 {
    background-image: linear-gradient(transparent 16.6667%, #EEEEEE 0%, #EEEEEE 83.3333%, transparent 0%) !important;
    background-size: 100% 40.3846rpx;
    position: relative !important;
  }
.sk-text-16-6667-365 {
    background-image: linear-gradient(transparent 16.6667%, #EEEEEE 0%, #EEEEEE 83.3333%, transparent 0%) !important;
    background-size: 100% 46.1538rpx;
    position: relative !important;
  }
.sk-text-16-6667-385 {
    background-image: linear-gradient(transparent 16.6667%, #EEEEEE 0%, #EEEEEE 83.3333%, transparent 0%) !important;
    background-size: 100% 40.3846rpx;
    position: relative !important;
  }
.sk-text-16-6667-286 {
    background-image: linear-gradient(transparent 16.6667%, #EEEEEE 0%, #EEEEEE 83.3333%, transparent 0%) !important;
    background-size: 100% 40.3846rpx;
    position: relative !important;
  }
.sk-text-16-6667-161 {
    background-image: linear-gradient(transparent 16.6667%, #EEEEEE 0%, #EEEEEE 83.3333%, transparent 0%) !important;
    background-size: 100% 40.3846rpx;
    position: relative !important;
  }
.sk-text-16-6667-624 {
    background-image: linear-gradient(transparent 16.6667%, #EEEEEE 0%, #EEEEEE 83.3333%, transparent 0%) !important;
    background-size: 100% 40.3846rpx;
    position: relative !important;
  }
.sk-button {
    color: #EFEFEF !important;
    background: #EFEFEF !important;
    border: none !important;
    box-shadow: none !important;
  }
.sk-image {
    background: #EFEFEF !important;
  }
.sk-pseudo::before, .sk-pseudo::after {
      background: #EFEFEF !important;
      background-image: none !important;
      color: transparent !important;
      border-color: transparent !important;
    }
.sk-pseudo-rect::before, .sk-pseudo-rect::after {
      border-radius: 0 !important;
    }
.sk-pseudo-circle::before, .sk-pseudo-circle::after {
      border-radius: 50% !important;
    }
.sk-container {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    background-color: transparent;
  }
