/**invite.wxss**/
/* 页面根元素样式 - 强制覆盖全局样式 */
page {
  background-color: #f8f4e9 !important; /* 更改为与首页匹配的米色背景 */
  height: 100vh !important;
  width: 100vw !important;
  overflow: hidden !important;
  position: relative !important;
}

/* 背景麻将装饰元素 */
.bg-elements {
  position: fixed;
  width: 100%;
  height: 100%;
  z-index: 1;
  pointer-events: none; /* 不阻挡点击事件 */
}

.mahjong-tile {
  position: absolute;
  width: 80rpx;
  height: 100rpx;
  background-color: #ffffff;
  border-radius: 8rpx;
  box-shadow: 0 4rpx 8rpx rgba(0,0,0,0.15);
  transform: rotate(var(--rotation));
  opacity: 0.1;
  z-index: -1;
}

.mahjong-1 {
  --rotation: 15deg;
  top: 10%;
  left: 10%;
}

.mahjong-2 {
  --rotation: -10deg;
  top: 20%;
  right: 15%;
}

.mahjong-3 {
  --rotation: 5deg;
  bottom: 25%;
  left: 5%;
}

.mahjong-4 {
  --rotation: -20deg;
  bottom: 15%;
  right: 10%;
}

/* 容器样式 */
.container {
  min-height: 100vh !important;
  height: 100vh !important;
  width: 100vw !important;
  background-color: #f8f4e9 !important; /* 与首页匹配的米色背景 */
  position: relative !important;
  z-index: 1000 !important;
  overflow: hidden !important;
  display: flex !important;
  flex-direction: column !important;
}

/* 自定义导航栏右侧操作按钮 */
.nav-action {
  width: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-icon {
  font-size: 36rpx;
  font-weight: 600;
  color: #8B0000; /* 更改为深红色 */
}

/* 滚动视图样式 */
.score-scroll-view {
  width: 100%;
  box-sizing: border-box;
  background-color: #f8f4e9; /* 与首页匹配的米色背景 */
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1002;
  overflow-x: hidden;
  padding-top: 0; /* 移除内边距，改用margin-top */
}

.content {
  padding: 32rpx;
  padding-top: 24rpx; /* 增加顶部内边距 */
  padding-bottom: 120rpx; /* 确保底部有足够空间 */
  margin-top: 0 !important; /* 覆盖全局样式的 margin-top: 88px */
  background-color: #f8f4e9; /* 与首页匹配的米色背景 */
  position: relative;
  z-index: 1003;
  box-sizing: border-box;
}

/* 标题样式 */
.mahjong-section-title {
  position: relative;
  display: inline-block;
  padding: 8rpx 16rpx;
  background: linear-gradient(135deg, #e6c46c, #d4af37);
  border-radius: 8rpx;
  color: #8B0000;
  font-weight: 600;
  box-shadow: 0 4rpx 12rpx rgba(212, 175, 55, 0.3);
  transform: rotate(-2deg);
  font-size: 32rpx;
  margin-bottom: 16rpx;
}

/* 游戏信息卡片 */
.game-info-card {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 8rpx 24rpx rgba(0,0,0,0.08), 0 2rpx 8rpx rgba(0,0,0,0.04);
  border-left: 4rpx solid #d4af37; /* 金色边框 */
}

.game-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.game-name {
  font-size: 36rpx;
  font-weight: 600;
  color: #8B0000; /* 深红色 */
  margin-bottom: 12rpx;
}

.game-details {
  font-size: 28rpx;
  color: #666;
}

/* 邀请方式区域 */
.invite-section {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 8rpx 24rpx rgba(0,0,0,0.08), 0 2rpx 8rpx rgba(0,0,0,0.04);
  border-left: 4rpx solid #d4af37; /* 金色边框 */
}

/* 邀请方式网格布局 */
.invite-methods-grid {
  display: flex;
  gap: 16rpx;
}

.invite-method-card {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 32rpx 16rpx;
  background-color: rgba(248, 244, 233, 0.6); /* 米色背景 */
  border-radius: 16rpx;
  border: 2rpx solid rgba(212, 175, 55, 0.2); /* 金色边框 */
  transition: all 0.3s ease;
  text-align: center;
  min-height: 200rpx;
  justify-content: center;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.06);
}

/* 为不同卡片添加特色背景 */
.invite-method-card:nth-child(1) {
  background: linear-gradient(135deg, rgba(230, 196, 108, 0.1), rgba(248, 244, 233, 0.6));
}

.invite-method-card:nth-child(1):hover::before,
.invite-method-card:nth-child(1):active::before {
  background: linear-gradient(135deg, rgba(212, 175, 55, 0.15), rgba(212, 175, 55, 0.08));
}

.invite-method-card:nth-child(2) {
  background: linear-gradient(135deg, rgba(230, 196, 108, 0.15), rgba(248, 244, 233, 0.6));
}

.invite-method-card:nth-child(2):hover::before,
.invite-method-card:nth-child(2):active::before {
  background: linear-gradient(135deg, rgba(212, 175, 55, 0.15), rgba(212, 175, 55, 0.08));
}

.invite-method-card:nth-child(3),
.share-card {
  background: linear-gradient(135deg, rgba(230, 196, 108, 0.2), rgba(248, 244, 233, 0.6));
}

.invite-method-card:nth-child(3):hover::before,
.invite-method-card:nth-child(3):active::before,
.share-card:hover::before,
.share-card:active::before {
  background: linear-gradient(135deg, rgba(212, 175, 55, 0.15), rgba(212, 175, 55, 0.08));
}

.share-card:active::before {
  background: linear-gradient(135deg, rgba(212, 175, 55, 0.15), rgba(212, 175, 55, 0.08));
}

/* 通用hover效果 */
.invite-method-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.invite-method-card:hover::before,
.invite-method-card:active::before {
  opacity: 1;
}

.invite-method-card:hover,
.invite-method-card:active {
  transform: translateY(-4rpx);
  box-shadow: 0 8rpx 24rpx rgba(212, 175, 55, 0.2);
  border-color: #d4af37;
}

.share-card {
  border: 2rpx solid rgba(212, 175, 55, 0.2); /* 金色边框 */
  background-color: rgba(248, 244, 233, 0.6); /* 米色背景 */
  font-size: inherit;
  line-height: inherit;
  padding: 32rpx 16rpx;
}

.share-card::after {
  border: none;
}

.card-icon {
  font-size: 64rpx;
  margin-bottom: 16rpx;
  filter: drop-shadow(0 2rpx 4rpx rgba(0,0,0,0.1));
  position: relative;
  z-index: 1;
}

.card-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #8B0000; /* 深红色 */
  margin-bottom: 8rpx;
  position: relative;
  z-index: 1;
}

.card-desc {
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
  position: relative;
  z-index: 1;
}

/* 响应式优化 */
@media screen and (max-width: 600rpx) {
  .invite-methods-grid {
    flex-direction: column;
    gap: 12rpx;
  }
  
  .invite-method-card {
    flex-direction: row;
    min-height: auto;
    padding: 24rpx;
    text-align: left;
  }
  
  .card-icon {
    font-size: 48rpx;
    margin-bottom: 0;
    margin-right: 24rpx;
    flex-shrink: 0;
  }
  
  .card-content {
    flex: 1;
  }
  
  .card-title {
    margin-bottom: 4rpx;
  }
}

/* 为移动端模式添加内容包装 */
.invite-method-card .card-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

@media screen and (max-width: 600rpx) {
  .invite-method-card .card-content {
    align-items: flex-start;
    text-align: left;
  }
}

/* 邀请链接卡片 */
.invite-link-card {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 8rpx 24rpx rgba(0,0,0,0.08), 0 2rpx 8rpx rgba(0,0,0,0.04);
  border-left: 4rpx solid #d4af37; /* 金色边框 */
}

.link-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.link-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #8B0000; /* 深红色 */
}

.copy-btn {
  padding: 12rpx 24rpx;
  background: linear-gradient(135deg, #e6c46c, #d4af37); /* 金色渐变 */
  color: #8B0000; /* 深红色 */
  border-radius: 8rpx;
  font-size: 24rpx;
  border: none;
  box-shadow: 0 4rpx 12rpx rgba(212, 175, 55, 0.3);
}

.link-content {
  padding: 24rpx;
  background-color: rgba(248, 244, 233, 0.6); /* 米色背景 */
  border-radius: 8rpx;
  font-size: 24rpx;
  color: #666;
  word-break: break-all;
  line-height: 1.5;
  border: 1rpx solid rgba(212, 175, 55, 0.2); /* 金色边框 */
}

/* 二维码卡片 */
.qr-code-card {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  text-align: center;
  box-shadow: 0 8rpx 24rpx rgba(0,0,0,0.08), 0 2rpx 8rpx rgba(0,0,0,0.04);
  border-left: 4rpx solid #d4af37; /* 金色边框 */
}

.qr-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.qr-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #8B0000; /* 深红色 */
}

.save-btn {
  padding: 12rpx 24rpx;
  background: linear-gradient(135deg, #e6c46c, #d4af37); /* 金色渐变 */
  color: #8B0000; /* 深红色 */
  border-radius: 8rpx;
  font-size: 24rpx;
  border: none;
  box-shadow: 0 4rpx 12rpx rgba(212, 175, 55, 0.3);
}

.qr-image {
  width: 400rpx;
  height: 400rpx;
  border-radius: 8rpx;
  border: 4rpx solid rgba(212, 175, 55, 0.2); /* 金色边框 */
}

/* 已邀请玩家列表 */
.invited-players {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 8rpx 24rpx rgba(0,0,0,0.08), 0 2rpx 8rpx rgba(0,0,0,0.04);
  border-left: 4rpx solid #d4af37; /* 金色边框 */
}

.player-list {
  margin-top: 24rpx;
}

.player-item {
  display: flex;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 1rpx solid rgba(212, 175, 55, 0.2); /* 金色边框 */
}

.player-item:last-child {
  border-bottom: none;
}

.player-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 16rpx; /* 更圆润的边角 */
  margin-right: 24rpx;
  border: 2rpx solid rgba(212, 175, 55, 0.3); /* 金色边框 */
}

.player-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.player-name {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
}

.player-status {
  font-size: 24rpx;
  color: #8B0000; /* 深红色 */
}

.player-status-icon {
  font-size: 32rpx;
  width: 40rpx;
  text-align: center;
}

.player-status-icon.joined {
  color: #52c41a;
}

.player-status-icon.invited {
  color: #d4af37; /* 金色 */
}

/* 底部操作按钮 */
.action-buttons {
  display: flex;
  gap: 24rpx;
  padding: 0 32rpx;
}

.btn {
  flex: 1;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 16rpx;
  font-size: 32rpx;
  font-weight: 500;
  border: none;
  box-shadow: 0 8rpx 24rpx rgba(212, 175, 55, 0.3); /* 金色阴影 */
}

.btn-secondary {
  background-color: rgba(248, 244, 233, 0.8); /* 米色背景 */
  color: #8B0000; /* 深红色 */
  border: 1rpx solid #d4af37; /* 金色边框 */
}

.btn-primary {
  background: linear-gradient(135deg, #e6c46c, #d4af37); /* 金色渐变 */
  color: #8B0000; /* 深红色 */
}

button[disabled] {
  background-color: #cccccc !important;
  color: #ffffff !important;
  opacity: 0.6;
} 