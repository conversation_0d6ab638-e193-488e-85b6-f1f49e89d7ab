<!--invite.wxml-->
<view class="container">
  <!-- 背景麻将装饰元素 -->
  <view class="bg-elements">
    <view class="mahjong-tile mahjong-1"></view>
    <view class="mahjong-tile mahjong-2"></view>
    <view class="mahjong-tile mahjong-3"></view>
    <view class="mahjong-tile mahjong-4"></view>
  </view>

  <!-- 自定义导航栏 -->
  <custom-navbar
    title="邀请好友"
    show-back="{{true}}"
    show-home="{{true}}"
    bind:height="onNavbarHeight"
  >
    <!-- 右侧操作按钮 -->
    <view slot="right" class="nav-action">
      <text class="action-icon">⋮</text>
    </view>
  </custom-navbar>
  
  <!-- 内容区域 -->
  <scroll-view 
    class="score-scroll-view" 
    scroll-y="true" 
    enhanced="{{true}}" 
    show-scrollbar="{{false}}"
    style="height: calc(100vh - {{navbarHeight}}px); padding-top: {{navbarHeight}}px;">
    <view class="content">
      <!-- 游戏信息展示 -->
      <view class="game-info-card">
        <view class="game-header">
          <view class="game-name">{{gameInfo.name}}</view>
          <view class="game-details">{{gameInfo.playerCount}}人 · {{gameTypeName}}</view>
        </view>
      </view>

      <!-- 邀请方式 -->
      <view class="invite-section">
        <view class="mahjong-section-title">邀请方式</view>
        
        <view class="invite-methods-grid">
          <!-- 分享链接 -->
          <view class="invite-method-card" bindtap="generateInviteLink">
            <view class="card-icon">🔗</view>
            <view class="card-content">
              <view class="card-title">分享链接</view>
              <view class="card-desc">生成邀请链接</view>
            </view>
          </view>

          <!-- 二维码邀请 -->
          <view class="invite-method-card" bindtap="generateQRCode">
            <view class="card-icon">📱</view>
            <view class="card-content">
              <view class="card-title">小程序码</view>
              <view class="card-desc">扫码加入游戏</view>
            </view>
          </view>

          <!-- 微信分享 -->
          <button class="invite-method-card share-card" bindtap="shareToWechat" open-type="share">
            <view class="card-icon">💬</view>
            <view class="card-content">
              <view class="card-title">微信分享</view>
              <view class="card-desc">分享到群聊</view>
            </view>
          </button>
        </view>
      </view>

      <!-- 邀请链接显示 -->
      <view class="invite-link-card" wx:if="{{inviteLink}}">
        <view class="link-header">
          <text class="link-title">邀请链接</text>
          <button class="copy-btn" bindtap="copyLink">复制</button>
        </view>
        <view class="link-content">{{inviteLink}}</view>
      </view>

      <!-- 二维码显示 -->
      <view class="qr-code-card" wx:if="{{qrCodeUrl}}">
        <view class="qr-header">
          <text class="qr-title">小程序码</text>
          <button class="save-btn" bindtap="saveQRCode">保存</button>
        </view>
        <image class="qr-image" src="{{qrCodeUrl}}" mode="aspectFit"></image>
      </view>

      <!-- 已邀请玩家列表 -->
      <view class="invited-players" wx:if="{{invitedPlayers.length > 0}}">
        <view class="mahjong-section-title">已邀请玩家 ({{invitedPlayers.length}}/{{gameInfo.playerCount}})</view>
        <view class="player-list">
          <view class="player-item" wx:for="{{invitedPlayers}}" wx:key="id">
            <image class="player-avatar" src="{{item.avatar || '/static/images/default-avatar.png'}}" mode="aspectFill"></image>
            <view class="player-info">
              <text class="player-name">{{item.name}}</text>
              <text class="player-status">{{item.status === 'joined' ? '已加入' : '邀请中'}}</text>
            </view>
            <view class="player-status-icon {{item.status}}">
              {{item.status === 'joined' ? '✓' : '⏳'}}
            </view>
          </view>
        </view>
      </view>

      <!-- 底部操作 -->
      <view class="action-buttons">
        <button class="btn btn-secondary" bindtap="refreshStatus">刷新状态</button>
        <button 
          class="btn btn-primary" 
          bindtap="startGame" 
          disabled="{{!canStartGame}}"
        >
          开始游戏 ({{joinedCount}}/{{gameInfo.playerCount}})
        </button>
      </view>
    </view>
  </scroll-view>
</view>
