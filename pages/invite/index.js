// invite.js
const router = require('../../utils/router');

Page({
    data: {
        statusBarHeight: 20,
        navbarHeight: 0,
        gameInfo: {},
        gameTypeName: '',
        inviteLink: '',
        qrCodeUrl: '',
        invitedPlayers: [],
        joinedCount: 0,
        canStartGame: false,
        inviteId: ''
    },

    onLoad() {
        // 获取系统信息设置状态栏高度
        const systemInfo = wx.getSystemInfoSync();
        this.setData({
            statusBarHeight: systemInfo.statusBarHeight
        });

        // 获取游戏信息
        const gameInfo = wx.getStorageSync('currentGameInfo');
        if (!gameInfo) {
            wx.showToast({
                title: '游戏信息获取失败',
                icon: 'error'
            });
            setTimeout(() => {
                wx.navigateBack();
            }, 1500);
            return;
        }

        // 生成邀请ID
        const inviteId = this.generateInviteId();

        // 设置游戏类型名称
        const gameTypeName = this.getGameTypeName(gameInfo.type);

        this.setData({
            gameInfo,
            gameTypeName,
            inviteId
        });

        // 保存邀请信息到本地
        this.saveInviteInfo();
    },

    onShow() {
        // 每次显示页面时刷新状态
        this.refreshStatus();
    },

    // 自定义导航栏高度设置
    onNavbarHeight(e) {
        this.setData({
            navbarHeight: e.detail
        });
    },

    navigateBack() {
        // 检查页面栈
        const pages = getCurrentPages();
        if (pages.length > 1) {
            router.navigateBack();
        } else {
            // 如果没有上一页，返回首页
            router.switchTab('gameList');
        }
    },

    generateInviteId() {
        // 生成唯一的邀请ID
        const timestamp = Date.now().toString(36);
        const random = Math.random().toString(36).substr(2, 5);
        return `inv_${timestamp}_${random}`;
    },

    getGameTypeName(type) {
        const typeMap = {
            'mahjong': '麻将',
            'poker': '扑克',
            'board': '棋类',
            'other': '其他'
        };
        return typeMap[type] || '未知';
    },

    saveInviteInfo() {
        const inviteInfo = {
            id: this.data.inviteId,
            gameInfo: this.data.gameInfo,
            createTime: Date.now(),
            players: [],
            status: 'waiting' // waiting, started, ended
        };

        // 保存到本地存储（实际项目中应该保存到服务器）
        wx.setStorageSync(`invite_${this.data.inviteId}`, inviteInfo);
    },

    generateInviteLink() {
        wx.showLoading({
            title: '生成中...'
        });

        // 模拟生成邀请链接
        setTimeout(() => {
            const baseUrl = 'https://your-domain.com/invite';
            const inviteLink = `${baseUrl}?id=${this.data.inviteId}`;

            this.setData({
                inviteLink
            });

            wx.hideLoading();
            wx.showToast({
                title: '链接生成成功',
                icon: 'success'
            });
        }, 1000);
    },

    generateQRCode() {
        wx.showLoading({
            title: '生成中...'
        });

        // 模拟生成二维码（实际项目中需要调用小程序码接口）
        setTimeout(() => {
            // 这里应该调用后端接口生成小程序码
            const qrCodeUrl = '/static/images/temp-qr.png'; // 临时占位图片

            this.setData({
                qrCodeUrl
            });

            wx.hideLoading();
            wx.showToast({
                title: '小程序码生成成功',
                icon: 'success'
            });
        }, 1000);
    },

    shareToWechat() {
        // 微信分享逻辑在 onShareAppMessage 中处理
        console.log('分享到微信');
    },

    copyLink() {
        if (!this.data.inviteLink) {
            wx.showToast({
                title: '请先生成链接',
                icon: 'error'
            });
            return;
        }

        wx.setClipboardData({
            data: this.data.inviteLink,
            success: () => {
                wx.showToast({
                    title: '链接已复制',
                    icon: 'success'
                });
            }
        });
    },

    saveQRCode() {
        if (!this.data.qrCodeUrl) {
            wx.showToast({
                title: '请先生成小程序码',
                icon: 'error'
            });
            return;
        }

        wx.saveImageToPhotosAlbum({
            filePath: this.data.qrCodeUrl,
            success: () => {
                wx.showToast({
                    title: '保存成功',
                    icon: 'success'
                });
            },
            fail: () => {
                wx.showToast({
                    title: '保存失败',
                    icon: 'error'
                });
            }
        });
    },

    refreshStatus() {
        // 模拟刷新邀请状态
        const inviteInfo = wx.getStorageSync(`invite_${this.data.inviteId}`);
        if (inviteInfo && inviteInfo.players) {
            const joinedCount = inviteInfo.players.filter(p => p.status === 'joined').length;
            const canStartGame = joinedCount >= this.data.gameInfo.playerCount;

            this.setData({
                invitedPlayers: inviteInfo.players,
                joinedCount,
                canStartGame
            });
        }
    },

    startGame() {
        if (!this.data.canStartGame) {
            wx.showToast({
                title: '等待更多玩家加入',
                icon: 'error'
            });
            return;
        }

        // 创建游戏并跳转到记分页面
        const gameData = {
            ...this.data.gameInfo,
            id: Date.now().toString(),
            createTime: Date.now(),
            players: this.data.invitedPlayers.map((player, index) => ({
                id: player.id,
                name: player.name,
                avatar: player.avatar,
                score: this.data.gameInfo.initialScore,
                selected: false
            })),
            rounds: []
        };

        // 保存游戏数据
        const games = wx.getStorageSync('games') || [];
        games.unshift(gameData);
        wx.setStorageSync('games', games);

        // 跳转到记分页面
        router.redirectTo('score', { id: gameData.id });
    },

    // 分享配置
    onShareAppMessage() {
        return {
            title: `邀请你加入「${this.data.gameInfo.name}」`,
            path: `/pages/join/index?inviteId=${this.data.inviteId}`,
            imageUrl: '/static/images/share-cover.png'
        };
    }
});